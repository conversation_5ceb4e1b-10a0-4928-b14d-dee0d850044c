import React from 'react';
import omniSageLetterLogo from '../../assets/Images/omnisage_ai_logo_transparent.png';
import ls from 'local-storage';
import Avatar from '@mui/material/Avatar';
import { getInitials } from '../utils/helperUtils';
import { useNavigate } from 'react-router-dom';
import BusinessSelector from './components/BusinessSelector';

function Header() {
  const name = ls.get('userName');
  const navigate = useNavigate();

  return (
    <div className="w-full h-[60px] min-h-[60px] max-h-[60px] bg-white border-b border-gray-200 flex items-center justify-between px-4">
      {/* Business Header */}
      <div className="p-5 border-b border-gray-200">
        <div className="flex items-center justify-center">
          <img src={omniSageLetterLogo} alt="OmniSage AI" className="h-8 w-auto object-contain" />
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <BusinessSelector />
        <Avatar
          onClick={() => navigate('/profile')}
          className="!bg-accent1 font-semibold text-lg w-10 h-10 cursor-pointer shadow-lg hover:!bg-accent1-hover transition-colors duration-200"
        >
          {getInitials(name)}
        </Avatar>
      </div>
    </div>
  );
}

export default Header;
