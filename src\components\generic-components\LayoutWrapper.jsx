import React from 'react';
// import style from './layoutWrapper.module.scss';
// import Header from '../global/Header';
// import NavigationBar from '../screens/NavigationBar';
import { useAuth } from '../../contexts/AuthContext';
import Header from '../layout/Header';
import Sidebar from '../layout/Sidebar';

function LayoutWrapper({ children, className }) {
  const { isMobileScreen } = useAuth();
  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <div className="flex-1 overflow-y-auto">{children}</div>
      </div>
    </div>
  );
}
export default LayoutWrapper;
