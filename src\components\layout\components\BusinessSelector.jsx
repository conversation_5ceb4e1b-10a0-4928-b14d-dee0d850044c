import React, { useState, useEffect, useMemo } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import {
  OutlinedInput,
  CircularProgress,
  Avatar,
  Box,
  Typography,
  InputAdornment,
  Button,
  Paper,
  ClickAwayListener,
} from '@mui/material';
import { Business, Search as SearchIcon, KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';
import apiClient from '../../services/apiClient';
import useDebounce from '../../global/hooks/useDebounce';
import { getErrorMessage } from '../../utils/apiUtils';

const renderBusinessImage = (business) => {
  if (business?.business_image) {
    return <Avatar src={business.business_image} alt={business.business_name} sx={{ width: 32, height: 32 }} />;
  }
  return (
    <Avatar sx={{ width: 32, height: 32, bgcolor: '#f0f0f0' }}>
      <Business sx={{ color: '#666' }} />
    </Avatar>
  );
};

function BusinessSelector() {
  const [inputValue, setInputValue] = useState('');
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [open, setOpen] = useState(false);
  const { globSelectedBusiness, setGlobSelectedBusiness } = useAuth() || {};
  const debouncedSearchTerm = useDebounce(inputValue, 500);

  const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage, error } = useInfiniteQuery({
    queryKey: ['businesses', debouncedSearchTerm],
    queryFn: async ({ pageParam = 1 }) => {
      const params = {};
      if (debouncedSearchTerm) params.business_name = debouncedSearchTerm;
      if (pageParam) params.page = pageParam;

      const response = await apiClient.get('/api/get-businesses', { params });
      return response;
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage.next) return undefined;
      // Extract page number from next URL
      const url = new URL(lastPage.next);
      return url.searchParams.get('page');
    },
    staleTime: Infinity,
    cacheTime: Infinity,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  // Flatten all pages of results into a single array
  const businesses = useMemo(() => data?.pages.flatMap((page) => page.results) || [], [data]);

  useEffect(() => {
    if (businesses?.length > 0 && !selectedBusiness && !globSelectedBusiness) {
      const defaultBusiness = businesses[0];
      setSelectedBusiness(defaultBusiness);
      if (setGlobSelectedBusiness) {
        setGlobSelectedBusiness(defaultBusiness);
      }
    } else if (globSelectedBusiness) {
      setSelectedBusiness(globSelectedBusiness);
    }
  }, [businesses, selectedBusiness, globSelectedBusiness]);

  const handleBusinessChange = (event, newValue) => {
    setSelectedBusiness(newValue);
    if (setGlobSelectedBusiness) {
      setGlobSelectedBusiness(newValue);
    }
    setOpen(false);
    setInputValue('');
  };

  // Only show loading indicator when initially loading and not during search
  if (isLoading && !businesses.length && !open) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <CircularProgress size={20} sx={{ mr: 1 }} />
        <Typography variant="body2">Loading businesses...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Typography color="error" variant="body2">
        Error loading businesses: {getErrorMessage(error)}
      </Typography>
    );
  }

  // Custom selector that shows the selected business
  return (
    <ClickAwayListener
      onClickAway={() => {
        setOpen(false);
        setInputValue('');
      }}
    >
      <Box sx={{ position: 'relative', width: 250 }}>
        {/* Selected business display */}
        <Box
          onClick={() => setOpen(!open)}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1.5,
            padding: '8px 12px',
            border: '1px solid #e0e0e0',
            borderRadius: '4px',
            cursor: 'pointer',
            bgcolor: 'white',
            '&:hover': {
              bgcolor: '#f5f5f5',
            },
          }}
        >
          {selectedBusiness && renderBusinessImage(selectedBusiness)}
          <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
            <Typography variant="body2" noWrap sx={{ fontWeight: 'medium' }}>
              {selectedBusiness?.business_name || 'Select Business'}
            </Typography>
          </Box>
          {open ? (
            <KeyboardArrowUp fontSize="small" color="action" />
          ) : (
            <KeyboardArrowDown fontSize="small" color="action" />
          )}
        </Box>

        {/* Dropdown */}
        {open && (
          <Paper
            elevation={4}
            sx={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              mt: 0.5,
              zIndex: 1300,
              maxHeight: 400,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            {/* Search field */}
            <OutlinedInput
              autoFocus
              placeholder="Search businesses"
              size="small"
              fullWidth
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              startAdornment={
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              }
              endAdornment={
                isLoading && (
                  <InputAdornment position="end">
                    <CircularProgress size={20} />
                  </InputAdornment>
                )
              }
              sx={{ m: 1, width: 'calc(100% - 16px)' }}
            />

            {/* Business list */}
            <Box
              sx={{
                overflow: 'auto',
                maxHeight: 320,
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#bdbdbd',
                  borderRadius: '4px',
                },
              }}
              onScroll={(event) => {
                const target = event.currentTarget;
                if (
                  target.scrollTop + target.clientHeight >= target.scrollHeight - 50 &&
                  hasNextPage &&
                  !isFetchingNextPage
                ) {
                  fetchNextPage();
                }
              }}
            >
              {businesses && businesses.length === 0 && !isLoading ? (
                <Typography sx={{ p: 2, textAlign: 'center' }}>No businesses found</Typography>
              ) : (
                businesses &&
                businesses.map((business) => (
                  <Box
                    key={business.business_id}
                    onClick={() => handleBusinessChange(null, business)}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1.5,
                      padding: '10px 16px',
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: '#f5f5f5',
                      },
                      bgcolor: selectedBusiness?.business_id === business.business_id ? '#f0f7ff' : 'transparent',
                    }}
                  >
                    {renderBusinessImage(business)}
                    <Box>
                      <Typography variant="body2">{business.business_name}</Typography>
                    </Box>
                  </Box>
                ))
              )}

              {/* Load more button */}
              {hasNextPage && (
                <Box sx={{ textAlign: 'center', p: 1 }}>
                  <Button size="small" onClick={() => fetchNextPage()} disabled={isFetchingNextPage}>
                    {isFetchingNextPage ? 'Loading...' : 'Load More'}
                  </Button>
                </Box>
              )}
            </Box>
          </Paper>
        )}
      </Box>
    </ClickAwayListener>
  );
}

export default BusinessSelector;
