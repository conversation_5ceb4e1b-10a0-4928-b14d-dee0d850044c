import React, { useState, useEffect, useMemo } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import {
  OutlinedInput,
  CircularProgress,
  Avatar,
  Box,
  Typography,
  InputAdornment,
  Button,
  Paper,
  ClickAwayListener,
} from '@mui/material';
import { Business, Search as SearchIcon, KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';
import apiClient from '../../services/apiClient';
import useDebounce from '../../global/hooks/useDebounce';

const renderBusinessImage = (business) => {
  if (business?.business_image) {
    return <Avatar src={business.business_image} alt={business.business_name} sx={{ width: 32, height: 32 }} />;
  }
  return (
    <Avatar sx={{ width: 32, height: 32, bgcolor: '#f0f0f0' }}>
      <Business sx={{ color: '#666' }} />
    </Avatar>
  );
};

function BusinessSelector() {
  const [inputValue, setInputValue] = useState('');
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [open, setOpen] = useState(false);
  const { globSelectedBusiness, setGlobSelectedBusiness } = useAuth() || {};
  const debouncedSearchTerm = useDebounce(inputValue, 500);

  const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage, error, refetch } = useInfiniteQuery({
    queryKey: ['businesses', debouncedSearchTerm],
    queryFn: async ({ pageParam = 1 }) => {
      const params = {};
      if (debouncedSearchTerm) params.business_name = debouncedSearchTerm;
      if (pageParam) params.page = pageParam;

      const response = await apiClient.get('/api/get-businesses', { params });
      return response;
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage.next) return undefined;
      // Extract page number from next URL
      const url = new URL(lastPage.next);
      return url.searchParams.get('page');
    },
    staleTime: Infinity,
    cacheTime: Infinity,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  // Flatten all pages of results into a single array
  const businesses = useMemo(() => data?.pages.flatMap((page) => page.results) || [], [data]);

  useEffect(() => {
    if (businesses?.length > 0 && !selectedBusiness && !globSelectedBusiness) {
      const defaultBusiness = businesses[0];
      setSelectedBusiness(defaultBusiness);
      if (setGlobSelectedBusiness) {
        setGlobSelectedBusiness(defaultBusiness);
      }
    } else if (globSelectedBusiness) {
      setSelectedBusiness(globSelectedBusiness);
    }
  }, [businesses, selectedBusiness, globSelectedBusiness]);

  const handleBusinessChange = (event, newValue) => {
    setSelectedBusiness(newValue);
    if (setGlobSelectedBusiness) {
      setGlobSelectedBusiness(newValue);
    }
    setOpen(false);
    setInputValue('');
  };

  // Initial loading state
  if (isLoading && !businesses.length && !open) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1.5,
          paddingX: '0.5em',
          border: '1px solid #e0e0e0',
          borderRadius: '4px',
          bgcolor: '#fafafa',
          width: 200,
          minHeight: 48,
        }}
      >
        <CircularProgress size={20} />
        <Typography variant="body2" sx={{ color: '#666', fontWeight: 'medium' }}>
          Loading businesses...
        </Typography>
      </Box>
    );
  }

  // Error
  if (error) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          gap: 1,
          paddingX: '0.5em',
          paddingY: '0.25em',
          border: '1px solid #f44336',
          borderRadius: '4px',
          bgcolor: '#ffeaea',
          width: 'fit-content',
          minHeight: 48,
        }}
      >
        <Typography color="error" variant="body2" sx={{ fontWeight: 'medium' }}>
          Failed to load businesses
        </Typography>
        <Button size="small" variant="outlined" color="error" onClick={() => refetch()}>
          Retry
        </Button>
      </Box>
    );
  }

  // Custom selector that shows the selected business
  return (
    <ClickAwayListener
      onClickAway={() => {
        setOpen(false);
        setInputValue('');
      }}
    >
      <Box sx={{ position: 'relative', width: 250 }}>
        {/* Selected business display */}
        <Box
          onClick={() => setOpen(!open)}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1.5,
            padding: '0.5em',
            border: open ? '2px solid #1976d2' : '1px solid #e0e0e0',
            borderRadius: '8px',
            cursor: 'pointer',
            bgcolor: 'white',
            minHeight: 48,
            transition: 'all 0.2s ease-in-out',
            boxShadow: open ? '0 2px 8px rgba(25, 118, 210, 0.15)' : '0 1px 3px rgba(0, 0, 0, 0.05)',
            '&:hover': {
              bgcolor: '#f8f9fa',
              borderColor: open ? '#1976d2' : '#bdbdbd',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            },
          }}
        >
          {selectedBusiness ? (
            <>
              {renderBusinessImage(selectedBusiness)}
              <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
                <Typography
                  variant="body2"
                  noWrap
                  sx={{
                    fontWeight: 600,
                    color: '#1a1a1a',
                    fontSize: '0.875rem',
                  }}
                >
                  {selectedBusiness.business_name}
                </Typography>
              </Box>
            </>
          ) : (
            <>
              <Avatar sx={{ width: 32, height: 32, bgcolor: '#f0f0f0' }}>
                <Business sx={{ color: '#999', fontSize: 18 }} />
              </Avatar>
              <Box sx={{ flexGrow: 1 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: '#999',
                    fontWeight: 'medium',
                    fontSize: '0.875rem',
                  }}
                >
                  Select a business
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: '#bbb',
                    fontSize: '0.75rem',
                  }}
                >
                  Choose from available businesses
                </Typography>
              </Box>
            </>
          )}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              color: open ? '#1976d2' : '#999',
              transition: 'color 0.2s ease-in-out',
            }}
          >
            {open ? <KeyboardArrowUp fontSize="small" /> : <KeyboardArrowDown fontSize="small" />}
          </Box>
        </Box>

        {/* Enhanced dropdown */}
        {open && (
          <Paper
            elevation={8}
            sx={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              mt: 0.5,
              zIndex: 1300,
              maxHeight: 400,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              borderRadius: '8px',
              border: '1px solid #e0e0e0',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
            }}
          >
            {/* Search field */}
            <OutlinedInput
              autoFocus
              placeholder="Search businesses by name..."
              size="small"
              fullWidth
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              startAdornment={
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" sx={{ color: '#666' }} />
                </InputAdornment>
              }
              endAdornment={
                isLoading && (
                  <InputAdornment position="end">
                    <CircularProgress size={18} sx={{ color: '#1976d2' }} />
                  </InputAdornment>
                )
              }
              sx={{
                m: 1.5,
                width: 'calc(100% - 24px)',
                '& .MuiOutlinedInput-root': {
                  borderRadius: '6px',
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#1976d2',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#1976d2',
                    borderWidth: '2px',
                  },
                },
                '& .MuiOutlinedInput-input': {
                  fontSize: '0.875rem',
                },
              }}
            />

            {/* Business list */}
            <Box
              sx={{
                overflow: 'auto',
                maxHeight: 320,
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#bdbdbd',
                  borderRadius: '4px',
                },
              }}
              onScroll={(event) => {
                const target = event.currentTarget;
                if (
                  target.scrollTop + target.clientHeight >= target.scrollHeight - 50 &&
                  hasNextPage &&
                  !isFetchingNextPage
                ) {
                  fetchNextPage();
                }
              }}
            >
              {businesses && businesses.length === 0 && !isLoading ? (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Business sx={{ fontSize: 48, color: '#ddd', mb: 1 }} />
                  <Typography variant="body2" sx={{ color: '#666', fontWeight: 'medium' }}>
                    No businesses found
                  </Typography>
                  <Typography variant="caption" sx={{ color: '#999' }}>
                    Try adjusting your search terms
                  </Typography>
                </Box>
              ) : (
                businesses &&
                businesses.map((business) => (
                  <Box
                    key={business.business_id}
                    onClick={() => handleBusinessChange(null, business)}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1.5,
                      padding: '12px 16px',
                      cursor: 'pointer',
                      transition: 'all 0.15s ease-in-out',
                      borderLeft:
                        selectedBusiness?.business_id === business.business_id
                          ? '3px solid #1976d2'
                          : '3px solid transparent',
                      '&:hover': {
                        bgcolor: '#f8f9fa',
                        borderLeftColor: '#1976d2',
                      },
                      bgcolor: selectedBusiness?.business_id === business.business_id ? '#f0f7ff' : 'transparent',
                    }}
                  >
                    {renderBusinessImage(business)}
                    <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
                      <Typography
                        variant="body2"
                        noWrap
                        sx={{
                          fontWeight: selectedBusiness?.business_id === business.business_id ? 600 : 'medium',
                          color: selectedBusiness?.business_id === business.business_id ? '#1976d2' : '#1a1a1a',
                        }}
                      >
                        {business.business_name}
                      </Typography>
                    </Box>
                  </Box>
                ))
              )}

              {/* Load more button */}
              {hasNextPage && (
                <Box sx={{ textAlign: 'center', p: 2, borderTop: '1px solid #f0f0f0' }}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => fetchNextPage()}
                    disabled={isFetchingNextPage}
                    startIcon={isFetchingNextPage && <CircularProgress size={16} />}
                    sx={{
                      borderRadius: '6px',
                      textTransform: 'none',
                      fontWeight: 'medium',
                      '&:hover': {
                        bgcolor: '#f8f9fa',
                      },
                    }}
                  >
                    {isFetchingNextPage ? 'Loading more...' : 'Load More Businesses'}
                  </Button>
                </Box>
              )}
            </Box>
          </Paper>
        )}
      </Box>
    </ClickAwayListener>
  );
}

export default BusinessSelector;
