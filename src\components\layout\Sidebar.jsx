import React from 'react';
import { CloudUpload, Description, Build, Settings, Business } from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const menuItems = [
  {
    id: 'upload',
    label: 'Upload',
    icon: CloudUpload,
    description: 'Upload invoices',
    path: ['/', '/create-invoice'],
  },
  {
    id: 'invoices',
    label: 'Invoices',
    icon: Description,
    description: 'View and manage invoices',
    path: '/invoices',
  },
  {
    id: 'tools',
    label: 'Tools',
    icon: Build,
    description: 'Business tools and utilities',
    path: '/tools',
  },
  {
    id: 'config',
    label: 'Config',
    icon: Settings,
    description: 'Business Configurations',
    path: '/config',
  },
];

export default function Sidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const { globSelectedBusiness } = useAuth();

  const handleNavigation = (path) => {
    if (Array.isArray(path)) {
      navigate(path[0]);
    } else {
      navigate(path);
    }
  };

  return (
    <div className="w-[230px] max-w-[230px] min-w-[230px] bg-white border-r border-gray-200 flex flex-col min-h-screen max-h-screen">
      {/* Selected Business Info */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <SelectedBusinessInfo
          business_name={globSelectedBusiness?.business_name}
          business_type={globSelectedBusiness?.business_type}
          business_image={globSelectedBusiness?.business_image}
          IsBusinessSelected={!!globSelectedBusiness}
        />
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 py-4 px-2 overflow-y-auto">
        <ul className="space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = Array.isArray(item.path)
              ? item.path.some((path) => location.pathname === path)
              : location.pathname === item.path;

            return (
              <li key={item.id}>
                <button
                  onClick={() => handleNavigation(item.path)}
                  className={`w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-all duration-200 select-none border-l-3 ${
                    isActive
                      ? 'bg-accent1 text-white border-l-accent2 shadow-sm'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 border-l-transparent'
                  }`}
                >
                  <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-gray-400'}`} />
                  <div className="flex-1">
                    <p className={`font-bold text-base mb-0 ${isActive ? 'text-white' : 'text-gray-800'}`}>
                      {item.label}
                    </p>
                    <p className={`text-xs ${isActive ? 'text-white text-opacity-80' : 'text-gray-500'}`}>
                      {item.description}
                    </p>
                  </div>
                </button>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
}

function SelectedBusinessInfo({ IsBusinessSelected, business_name, business_type, business_image }) {
  if (!IsBusinessSelected) {
    return (
      <div className="text-center py-1">
        <div className="w-10 h-10 mx-auto mb-2 rounded bg-gray-200 flex items-center justify-center">
          <Business className="w-5 h-5 text-gray-400" />
        </div>
        <div className="text-sm font-medium text-gray-600 mb-1">No business selected</div>
        <div className="text-xs text-gray-500">Please select a business to continue</div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      <div className="w-10 h-10 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
        {business_image ? (
          <img src={business_image} alt="Business Logo" className="w-8 h-8 rounded object-cover" />
        ) : (
          <Business className="w-6 h-6 text-gray-500" />
        )}
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-semibold text-gray-900 truncate">{business_name}</div>
        {business_type && <div className="text-xs text-gray-600 mt-0.5">{business_type}</div>}
      </div>
    </div>
  );
}
